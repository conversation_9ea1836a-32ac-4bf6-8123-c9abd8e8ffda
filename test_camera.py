# -*- coding: utf-8 -*-
"""测试摄像头捕获和保存"""

import cv2
import os
import time

def test_camera():
    """测试摄像头功能"""
    print("启动摄像头测试...")
    
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("错误：无法打开摄像头")
        return
    
    # 设置分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("摄像头初始化成功")
    print(f"分辨率: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
    print("开始捕获图像，按Ctrl+C退出")
    
    # 创建输出目录
    output_dir = "camera_test"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    frame_count = 0
    
    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            
            if not ret:
                print("无法读取摄像头数据")
                break
            
            # 每秒保存一帧
            if frame_count % 30 == 0:
                filename = f"{output_dir}/test_frame_{frame_count:04d}.jpg"
                cv2.imwrite(filename, frame)
                print(f"保存: {filename}")
            
            frame_count += 1
            time.sleep(1.0/30)  # 30fps
            
    except KeyboardInterrupt:
        print("\n用户中断")
    
    finally:
        cap.release()
        print(f"测试完成，共处理 {frame_count} 帧")

if __name__ == "__main__":
    test_camera()
