# -*- coding: utf-8 -*-
"""测试A4纸中心点标注功能"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.image_processor import ImageProcessor
from config.settings import CameraSettings

def create_test_image():
    """创建包含黑色边框的测试图像"""
    # 创建白色背景
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 绘制A4纸外框（黑色）
    outer_rect = (200, 150, 400, 300)  # x, y, w, h
    cv2.rectangle(img, 
                 (outer_rect[0], outer_rect[1]), 
                 (outer_rect[0] + outer_rect[2], outer_rect[1] + outer_rect[3]), 
                 (0, 0, 0),  # 黑色
                 20)  # 粗边框
    
    # 绘制A4纸内框（黑色）
    inner_rect = (220, 170, 360, 260)
    cv2.rectangle(img, 
                 (inner_rect[0], inner_rect[1]), 
                 (inner_rect[0] + inner_rect[2], inner_rect[1] + inner_rect[3]), 
                 (0, 0, 0),  # 黑色
                 15)  # 稍细边框
    
    return img

def test_center_point_detection():
    """测试中心点检测功能"""
    print("=== A4纸中心点检测测试 ===")
    
    # 创建测试图像
    test_img = create_test_image()
    print("✅ 测试图像创建成功")
    
    # 初始化图像处理器
    processor = ImageProcessor()
    print("✅ 图像处理器初始化成功")
    
    # 处理图像
    result_img = processor.process_frame(test_img)
    print("✅ 图像处理完成")
    
    # 保存结果
    cv2.imwrite("test_center_result.jpg", result_img)
    print("✅ 结果图像已保存为 test_center_result.jpg")
    
    # 检查配置
    print(f"中心点颜色: {CameraSettings.CENTER_POINT_COLOR}")
    print(f"中心点半径: {CameraSettings.CENTER_POINT_RADIUS}")
    print(f"边框颜色: {CameraSettings.BORDER_COLOR_OUTER}")
    
    return True

def main():
    """主测试函数"""
    print("开始A4纸中心点标注功能测试...\n")
    
    try:
        success = test_center_point_detection()
        
        if success:
            print("\n🎉 中心点检测功能测试成功！")
            print("请查看 test_center_result.jpg 文件查看结果")
            print("- 绿色线条：A4纸黑色边框的内外框")
            print("- 红色圆点：A4纸的中心点")
            print("- 坐标标注：显示中心点的像素坐标")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
