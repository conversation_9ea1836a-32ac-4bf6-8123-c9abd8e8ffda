# -*- coding: utf-8 -*-
"""摄像头参数校准指南和工具"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from config.settings import CameraSettings

def print_calibration_guide():
    """打印摄像头校准指南"""
    print("=" * 60)
    print("📷 摄像头深度测量校准指南")
    print("=" * 60)
    
    print("\n🎯 校准目标:")
    print("获得准确的摄像头焦距和传感器尺寸参数，以提高深度测量精度")
    
    print("\n📋 当前配置参数:")
    print(f"   焦距 (CAMERA_FOCAL_LENGTH_MM): {CameraSettings.CAMERA_FOCAL_LENGTH_MM}mm")
    print(f"   传感器宽度 (SENSOR_WIDTH_MM): {CameraSettings.SENSOR_WIDTH_MM}mm")
    print(f"   摄像头分辨率: 1920x1080")
    
    print("\n🔧 校准方法1: 已知距离校准")
    print("1. 将A4纸放在距离摄像头已知距离的位置（如50cm）")
    print("2. 运行程序，记录A4纸在图像中的像素尺寸")
    print("3. 使用公式反推焦距:")
    print("   焦距 = (像素尺寸 × 实际距离 × 传感器宽度) / (A4纸实际尺寸 × 图像宽度)")
    
    print("\n🔧 校准方法2: 多点测量校准")
    print("1. 在不同距离(30cm, 50cm, 100cm)放置A4纸")
    print("2. 记录每个距离下的像素尺寸")
    print("3. 使用最小二乘法拟合最佳参数")
    
    print("\n📐 常见摄像头参数参考:")
    camera_specs = [
        ("手机摄像头", "3.0-5.0mm", "3.2-4.8mm"),
        ("网络摄像头", "2.8-6.0mm", "3.0-5.0mm"),
        ("USB摄像头", "3.6mm", "3.68mm"),
        ("树莓派摄像头", "3.04mm", "3.76mm"),
    ]
    
    print("   摄像头类型    | 焦距范围    | 传感器宽度")
    print("   " + "-" * 45)
    for name, focal, sensor in camera_specs:
        print(f"   {name:<12} | {focal:<10} | {sensor}")
    
    print("\n⚙️  参数调整方法:")
    print("编辑 config/settings.py 文件，修改以下参数:")
    print("   CAMERA_FOCAL_LENGTH_MM = 你的焦距值")
    print("   SENSOR_WIDTH_MM = 你的传感器宽度值")

def calculate_focal_length_from_measurement():
    """根据实际测量计算焦距"""
    print("\n" + "=" * 60)
    print("🧮 焦距计算工具")
    print("=" * 60)
    
    try:
        print("\n请输入测量数据:")
        actual_distance_cm = float(input("实际距离 (cm): "))
        pixel_width = float(input("A4纸在图像中的像素宽度: "))
        image_width = float(input("图像宽度 (像素，默认1920): ") or "1920")
        
        # 使用A4纸宽度210mm作为参考
        a4_width_mm = 210
        actual_distance_mm = actual_distance_cm * 10
        sensor_width_mm = CameraSettings.SENSOR_WIDTH_MM
        
        # 计算焦距
        focal_length = (pixel_width * actual_distance_mm * sensor_width_mm) / (a4_width_mm * image_width)
        
        print(f"\n📊 计算结果:")
        print(f"   建议焦距: {focal_length:.2f}mm")
        print(f"   当前焦距: {CameraSettings.CAMERA_FOCAL_LENGTH_MM}mm")
        print(f"   差异: {abs(focal_length - CameraSettings.CAMERA_FOCAL_LENGTH_MM):.2f}mm")
        
        if abs(focal_length - CameraSettings.CAMERA_FOCAL_LENGTH_MM) > 0.5:
            print(f"\n⚠️  建议更新配置文件中的焦距值为: {focal_length:.2f}mm")
        else:
            print(f"\n✅ 当前焦距配置较为准确")
            
    except ValueError:
        print("❌ 输入格式错误，请输入数字")
    except Exception as e:
        print(f"❌ 计算过程中发生错误: {e}")

def estimate_accuracy():
    """估算当前配置的测量精度"""
    print("\n" + "=" * 60)
    print("📈 测量精度估算")
    print("=" * 60)
    
    distances = [30, 50, 100, 150, 200]  # 厘米
    
    print("\n在不同距离下的理论精度:")
    print("距离(cm) | 1像素误差对应的距离误差(mm)")
    print("-" * 45)
    
    for distance_cm in distances:
        distance_mm = distance_cm * 10
        
        # 计算1像素误差对应的实际距离误差
        # 误差 = (距离² × 传感器宽度) / (A4宽度 × 焦距 × 图像宽度)
        pixel_error_mm = (distance_mm ** 2 * CameraSettings.SENSOR_WIDTH_MM) / \
                        (210 * CameraSettings.CAMERA_FOCAL_LENGTH_MM * 1920)
        
        print(f"{distance_cm:6d}   | {pixel_error_mm:.2f}mm")
    
    print(f"\n💡 提示:")
    print(f"- 距离越远，测量误差越大")
    print(f"- 建议测量距离: 30-100cm 范围内精度最高")
    print(f"- A4纸边框检测精度直接影响深度测量精度")

def main():
    """主函数"""
    print_calibration_guide()
    
    while True:
        print("\n" + "=" * 60)
        print("选择操作:")
        print("1. 根据实际测量计算焦距")
        print("2. 估算当前配置的测量精度")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            calculate_focal_length_from_measurement()
        elif choice == "2":
            estimate_accuracy()
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
