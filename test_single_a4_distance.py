# -*- coding: utf-8 -*-
"""测试单张A4纸的鼠标交互距离测量功能"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.image_processor import ImageProcessor
from config.settings import CameraSettings

def create_single_a4_test_image():
    """创建包含单张A4纸的测试图像"""
    # 创建白色背景
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 绘制A4纸外框（黑色）
    outer_rect = (200, 150, 400, 300)  # x, y, w, h
    cv2.rectangle(img, 
                 (outer_rect[0], outer_rect[1]), 
                 (outer_rect[0] + outer_rect[2], outer_rect[1] + outer_rect[3]), 
                 (0, 0, 0),  # 黑色
                 20)  # 粗边框
    
    # 绘制A4纸内框（黑色）
    inner_rect = (220, 170, 360, 260)
    cv2.rectangle(img, 
                 (inner_rect[0], inner_rect[1]), 
                 (inner_rect[0] + inner_rect[2], inner_rect[1] + inner_rect[3]), 
                 (0, 0, 0),  # 黑色
                 15)  # 稍细边框
    
    return img

def test_mouse_interaction():
    """测试鼠标交互距离测量"""
    print("=== 单张A4纸鼠标交互距离测量测试 ===")
    
    # 创建测试图像
    test_img = create_single_a4_test_image()
    print("✅ 单张A4纸测试图像创建成功")
    
    # 初始化图像处理器
    processor = ImageProcessor()
    print("✅ 图像处理器初始化成功")
    
    # 处理图像
    result_img = processor.process_frame(test_img)
    print("✅ 图像处理完成")
    
    # 创建窗口并设置鼠标回调
    window_name = "Single A4 Distance Test"
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    cv2.setMouseCallback(window_name, processor.mouse_callback)
    
    print("\n📋 使用说明:")
    print("- 红色圆点: A4纸中心点")
    print("- 鼠标左键点击: 在任意位置点击测量距离")
    print("- 紫色圆点: 鼠标点击位置")
    print("- 黄色连线: 距离测量线")
    print("- 黄色文字: 实际物理距离")
    print("- 按 'q' 键退出")
    
    # 显示配置信息
    print(f"\n📏 测量配置:")
    print(f"   A4纸尺寸: {CameraSettings.A4_WIDTH_MM}mm × {CameraSettings.A4_HEIGHT_MM}mm")
    print(f"   距离单位: {CameraSettings.DISTANCE_UNIT}")
    print(f"   精度: {CameraSettings.DISTANCE_PRECISION}位小数")
    
    # 显示像素比例
    if hasattr(processor, 'pixel_to_mm_ratio') and processor.pixel_to_mm_ratio:
        print(f"   像素比例: 1px = {processor.pixel_to_mm_ratio:.3f}mm")
    
    # 交互循环
    while True:
        # 重新处理图像以更新鼠标点击效果
        current_result = processor.process_frame(test_img)
        
        # 显示图像
        cv2.imshow(window_name, current_result)
        
        # 检测按键
        key = cv2.waitKey(30) & 0xFF
        if key == ord('q') or key == 27:  # 'q' 或 ESC
            break
    
    # 保存最终结果
    cv2.imwrite("test_single_a4_mouse.jpg", current_result)
    print("✅ 最终结果已保存为 test_single_a4_mouse.jpg")
    
    cv2.destroyAllWindows()
    return True

def test_programmatic_distance():
    """程序化测试距离计算"""
    print("\n=== 程序化距离计算测试 ===")
    
    # 创建测试图像
    test_img = create_single_a4_test_image()
    processor = ImageProcessor()
    
    # 处理图像获取A4纸中心
    result_img = processor.process_frame(test_img)
    
    if processor.a4_center and processor.pixel_to_mm_ratio:
        # 模拟几个点击位置
        test_points = [
            (300, 200),  # A4纸左上角附近
            (500, 400),  # A4纸右下角附近
            (400, 300),  # A4纸中心附近
            (100, 100),  # 远离A4纸的位置
        ]
        
        print(f"A4纸中心点: {processor.a4_center}")
        print(f"像素比例: 1px = {processor.pixel_to_mm_ratio:.3f}mm")
        print("\n距离计算结果:")
        
        for i, point in enumerate(test_points, 1):
            distance_info = processor.calculate_distance_between_points(
                processor.a4_center, point, processor.pixel_to_mm_ratio)
            
            if distance_info:
                print(f"  点{i} {point}: {distance_info['mm_distance']:.1f}mm")
        
        print("✅ 程序化距离计算测试完成")
    else:
        print("❌ 无法获取A4纸中心点或像素比例")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始单张A4纸距离测量功能测试...\n")
    
    try:
        # 程序化测试
        success1 = test_programmatic_distance()
        
        # 交互式测试
        success2 = test_mouse_interaction()
        
        if success1 and success2:
            print("\n🎉 单张A4纸距离测量功能测试成功！")
            print("\n🔍 新功能特点:")
            print("- 专门针对单张A4纸优化")
            print("- 鼠标点击交互测量距离")
            print("- 实时显示到A4纸中心的距离")
            print("- 支持任意位置点击测量")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
