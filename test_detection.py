# -*- coding: utf-8 -*-
"""边框检测功能测试脚本"""

import cv2
import numpy as np
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.camera_capture import CameraCapture
from src.image_processor import ImageProcessor
from config.settings import CameraSettings

def test_basic_functionality():
    """基础功能测试"""
    print("=== 基础功能测试 ===")
    
    # 初始化组件
    camera = CameraCapture()
    processor = ImageProcessor()
    
    if not camera.is_opened():
        print("❌ 摄像头初始化失败")
        return False
    
    print("✅ 摄像头初始化成功")
    print("✅ 图像处理器初始化成功")
    
    # 测试单帧处理
    ret, frame = camera.read_frame()
    if not ret:
        print("❌ 无法读取摄像头帧")
        camera.release()
        return False
    
    print("✅ 摄像头帧读取成功")
    
    # 测试图像处理
    processed_frame = processor.process_frame(frame)
    if processed_frame is not None:
        print("✅ 图像处理成功")
    else:
        print("❌ 图像处理失败")
        camera.release()
        return False
    
    camera.release()
    return True

def test_performance():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    camera = CameraCapture()
    processor = ImageProcessor()
    
    if not camera.is_opened():
        print("❌ 摄像头初始化失败")
        return False
    
    frame_count = 0
    start_time = time.time()
    test_duration = 5  # 测试5秒
    
    print(f"开始 {test_duration} 秒性能测试...")
    
    while time.time() - start_time < test_duration:
        ret, frame = camera.read_frame()
        if not ret:
            break
        
        # 处理帧
        processed_frame = processor.process_frame(frame)
        frame_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_fps = frame_count / total_time
    
    print(f"✅ 性能测试完成")
    print(f"   处理帧数: {frame_count}")
    print(f"   测试时间: {total_time:.2f}秒")
    print(f"   平均帧率: {avg_fps:.2f} FPS")
    
    # 性能评估(针对Jetson设备调整标准)
    if avg_fps >= 10:
        print("✅ 性能优秀 (≥10 FPS)")
    elif avg_fps >= 5:
        print("✅ 性能良好 (≥5 FPS，适合Jetson设备)")
    elif avg_fps >= 2:
        print("⚠️  性能可接受 (≥2 FPS)")
    else:
        print("❌ 性能需要优化 (<2 FPS)")

    camera.release()
    return avg_fps >= 2  # 降低性能要求

def test_parameter_validation():
    """参数验证测试"""
    print("\n=== 参数验证测试 ===")
    
    # 测试配置验证
    errors = CameraSettings.validate_config()
    if not errors:
        print("✅ 配置参数验证通过")
    else:
        print("❌ 配置参数验证失败:")
        for error in errors:
            print(f"   - {error}")
        return False
    
    # 测试关键参数
    print(f"   Canny阈值: {CameraSettings.CANNY_THRESHOLD_LOW}-{CameraSettings.CANNY_THRESHOLD_HIGH}")
    print(f"   轮廓面积: {CameraSettings.MIN_CONTOUR_AREA}-{CameraSettings.MAX_CONTOUR_AREA}")
    print(f"   高斯模糊: {CameraSettings.GAUSSIAN_BLUR_SIZE}")
    print(f"   边框颜色: {CameraSettings.BORDER_COLOR_INNER}")
    
    return True

def test_edge_cases():
    """边缘情况测试"""
    print("\n=== 边缘情况测试 ===")
    
    processor = ImageProcessor()
    
    # 测试空图像
    empty_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    result = processor.process_frame(empty_frame)
    if result is not None:
        print("✅ 空图像处理正常")
    else:
        print("❌ 空图像处理失败")
        return False
    
    # 测试纯白图像
    white_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
    result = processor.process_frame(white_frame)
    if result is not None:
        print("✅ 纯白图像处理正常")
    else:
        print("❌ 纯白图像处理失败")
        return False
    
    # 测试检测开关
    original_state = CameraSettings.DETECTION_ENABLED
    CameraSettings.DETECTION_ENABLED = False
    
    result = processor.process_frame(empty_frame)
    if np.array_equal(result, empty_frame):
        print("✅ 检测开关功能正常")
    else:
        print("❌ 检测开关功能异常")
        CameraSettings.DETECTION_ENABLED = original_state
        return False
    
    CameraSettings.DETECTION_ENABLED = original_state
    return True

def main():
    """主测试函数"""
    print("开始边框检测功能全面测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("基础功能", test_basic_functionality()))
    test_results.append(("性能测试", test_performance()))
    test_results.append(("参数验证", test_parameter_validation()))
    test_results.append(("边缘情况", test_edge_cases()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！边框检测功能工作正常")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调优")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
