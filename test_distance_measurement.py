# -*- coding: utf-8 -*-
"""测试距离测量功能"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.image_processor import ImageProcessor
from config.settings import CameraSettings

def create_test_image_with_two_papers():
    """创建包含两张A4纸的测试图像"""
    # 创建白色背景
    img = np.ones((800, 1200, 3), dtype=np.uint8) * 255
    
    # 第一张A4纸（左侧）
    paper1_outer = (100, 200, 300, 400)  # x, y, w, h
    cv2.rectangle(img, 
                 (paper1_outer[0], paper1_outer[1]), 
                 (paper1_outer[0] + paper1_outer[2], paper1_outer[1] + paper1_outer[3]), 
                 (0, 0, 0), 15)
    
    paper1_inner = (115, 215, 270, 370)
    cv2.rectangle(img, 
                 (paper1_inner[0], paper1_inner[1]), 
                 (paper1_inner[0] + paper1_inner[2], paper1_inner[1] + paper1_inner[3]), 
                 (0, 0, 0), 10)
    
    # 第二张A4纸（右侧）
    paper2_outer = (600, 150, 300, 400)
    cv2.rectangle(img, 
                 (paper2_outer[0], paper2_outer[1]), 
                 (paper2_outer[0] + paper2_outer[2], paper2_outer[1] + paper2_outer[3]), 
                 (0, 0, 0), 15)
    
    paper2_inner = (615, 165, 270, 370)
    cv2.rectangle(img, 
                 (paper2_inner[0], paper2_inner[1]), 
                 (paper2_inner[0] + paper2_inner[2], paper2_inner[1] + paper2_inner[3]), 
                 (0, 0, 0), 10)
    
    return img

def test_distance_measurement():
    """测试距离测量功能"""
    print("=== 距离测量功能测试 ===")
    
    # 创建测试图像
    test_img = create_test_image_with_two_papers()
    print("✅ 包含两张A4纸的测试图像创建成功")
    
    # 初始化图像处理器
    processor = ImageProcessor()
    print("✅ 图像处理器初始化成功")
    
    # 处理图像
    result_img = processor.process_frame(test_img)
    print("✅ 图像处理完成")
    
    # 保存结果
    cv2.imwrite("test_distance_result.jpg", result_img)
    print("✅ 结果图像已保存为 test_distance_result.jpg")
    
    # 显示配置信息
    print(f"\n📏 距离测量配置:")
    print(f"   A4纸尺寸: {CameraSettings.A4_WIDTH_MM}mm × {CameraSettings.A4_HEIGHT_MM}mm")
    print(f"   距离单位: {CameraSettings.DISTANCE_UNIT}")
    print(f"   精度: {CameraSettings.DISTANCE_PRECISION}位小数")
    print(f"   测量功能: {'启用' if CameraSettings.DISTANCE_MEASUREMENT_ENABLED else '禁用'}")
    
    # 显示像素比例
    if hasattr(processor, 'pixel_to_mm_ratio') and processor.pixel_to_mm_ratio:
        print(f"   像素比例: 1px = {processor.pixel_to_mm_ratio:.3f}mm")
    
    return True

def test_single_paper_reference():
    """测试单张A4纸作为参考的情况"""
    print("\n=== 单张A4纸参考测试 ===")
    
    # 创建只有一张A4纸的图像
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 绘制A4纸
    outer_rect = (200, 150, 400, 300)
    cv2.rectangle(img, 
                 (outer_rect[0], outer_rect[1]), 
                 (outer_rect[0] + outer_rect[2], outer_rect[1] + outer_rect[3]), 
                 (0, 0, 0), 20)
    
    inner_rect = (220, 170, 360, 260)
    cv2.rectangle(img, 
                 (inner_rect[0], inner_rect[1]), 
                 (inner_rect[0] + inner_rect[2], inner_rect[1] + inner_rect[3]), 
                 (0, 0, 0), 15)
    
    # 处理图像
    processor = ImageProcessor()
    result_img = processor.process_frame(img)
    
    # 保存结果
    cv2.imwrite("test_single_paper.jpg", result_img)
    print("✅ 单张A4纸测试完成，结果保存为 test_single_paper.jpg")
    
    return True

def main():
    """主测试函数"""
    print("开始距离测量功能测试...\n")
    
    try:
        # 测试距离测量
        success1 = test_distance_measurement()
        
        # 测试单张纸参考
        success2 = test_single_paper_reference()
        
        if success1 and success2:
            print("\n🎉 距离测量功能测试成功！")
            print("\n📋 测试结果说明:")
            print("- test_distance_result.jpg: 两张A4纸的距离测量")
            print("- test_single_paper.jpg: 单张A4纸的参考标定")
            print("\n🔍 功能说明:")
            print("- 绿色线条: A4纸黑色边框")
            print("- 红色圆点: A4纸中心点")
            print("- 黄色连线: 中心点之间的距离测量")
            print("- 黄色文字: 实际物理距离(毫米)")
            print("- 白色文字: 像素比例信息")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
