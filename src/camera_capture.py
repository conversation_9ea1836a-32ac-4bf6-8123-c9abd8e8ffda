# -*- coding: utf-8 -*-
"""摄像头捕获核心类 - 封装所有摄像头操作"""

import cv2
import sys
import os

# 添加config目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.settings import CameraSettings

class CameraCapture:
    """摄像头捕获和显示核心类"""
    
    def __init__(self):
        """初始化摄像头"""
        self.cap = None
        self.is_initialized = False
        self._init_camera()
    
    def _init_camera(self):
        """初始化摄像头设备"""
        try:
            self.cap = cv2.VideoCapture(CameraSettings.CAMERA_ID)
            
            if not self.cap.isOpened():
                print(CameraSettings.ERROR_MESSAGES['camera_not_found'])
                return False
            
            # 设置摄像头分辨率
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, CameraSettings.WINDOW_WIDTH)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, CameraSettings.WINDOW_HEIGHT)
            
            self.is_initialized = True
            print(f"摄像头初始化成功 - 设备ID: {CameraSettings.CAMERA_ID}")
            return True
            
        except Exception as e:
            print(f"{CameraSettings.ERROR_MESSAGES['camera_init_failed']}: {e}")
            return False
    
    def read_frame(self):
        """读取视频帧"""
        if not self.is_opened():
            return False, None
        
        try:
            ret, frame = self.cap.read()
            if not ret:
                print(CameraSettings.ERROR_MESSAGES['frame_read_failed'])
                return False, None
            return True, frame
            
        except Exception as e:
            print(f"读取帧时发生错误: {e}")
            return False, None
    
    def is_opened(self):
        """检查摄像头是否可用"""
        return self.is_initialized and self.cap is not None and self.cap.isOpened()
    
    def get_fps(self):
        """获取摄像头帧率"""
        if self.is_opened():
            return self.cap.get(cv2.CAP_PROP_FPS)
        return 0
    
    def get_resolution(self):
        """获取摄像头分辨率"""
        if self.is_opened():
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            return width, height
        return 0, 0
    
    def release(self):
        """释放摄像头资源"""
        if self.cap is not None:
            self.cap.release()
            self.is_initialized = False
            print("摄像头资源已释放")
    
    def __del__(self):
        """析构函数，确保资源释放"""
        self.release()
