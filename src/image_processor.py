# -*- coding: utf-8 -*-
"""图像处理核心类 - 封装所有计算机视觉算法"""

import cv2
import numpy as np
import sys
import os

# 添加config目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.settings import CameraSettings

class ImageProcessor:
    """图像处理核心类 - 实现A4纸边框检测和绘制"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.settings = CameraSettings
        self.last_contours = []  # 缓存上一帧的轮廓
    
    def detect_black_regions(self, frame):
        """检测黑色区域 - 专门用于识别黑色边框"""
        try:
            # 转换为HSV色彩空间，更适合颜色检测
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # 定义黑色范围 (HSV)
            # 黑色在HSV中：H可以是任意值，S较低，V很低
            lower_black = np.array([0, 0, 0])      # 下限
            upper_black = np.array([180, 255, 50]) # 上限(V=50检测深色)

            # 创建黑色掩码
            black_mask = cv2.inRange(hsv, lower_black, upper_black)

            # 形态学操作去除噪点
            kernel = np.ones((3, 3), np.uint8)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_OPEN, kernel)

            return black_mask

        except Exception as e:
            print(f"黑色区域检测失败: {e}")
            return None
    
    def detect_black_contours(self, black_mask):
        """检测黑色区域的轮廓"""
        try:
            # 查找轮廓 - 使用RETR_TREE获取层次结构
            contours, hierarchy = cv2.findContours(black_mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            return contours, hierarchy

        except Exception as e:
            print(f"黑色轮廓检测失败: {e}")
            return [], None
    
    def find_border_frames(self, contours, hierarchy):
        """查找黑色边框的内外框"""
        border_pairs = []

        if hierarchy is None:
            return border_pairs

        # 遍历轮廓，寻找符合条件的黑色边框
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            # 面积筛选 - 黑色边框应该有一定大小
            if area < self.settings.MIN_CONTOUR_AREA or area > self.settings.MAX_CONTOUR_AREA:
                continue

            # 轮廓近似为矩形
            epsilon = self.settings.CONTOUR_APPROX_EPSILON * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 检查是否为矩形（4个顶点）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = max(w, h) / min(w, h)

                # 检查宽高比是否符合A4纸
                if abs(aspect_ratio - self.settings.A4_ASPECT_RATIO) <= self.settings.ASPECT_RATIO_TOLERANCE:
                    # 查找内部轮廓（内边框）
                    inner_contour = None

                    # 检查是否有子轮廓（内边框）
                    child_idx = hierarchy[0][i][2]  # 第一个子轮廓
                    if child_idx != -1:
                        child_contour = contours[child_idx]
                        child_area = cv2.contourArea(child_contour)

                        # 内边框应该比外边框小
                        if child_area < area * 0.8:  # 内框面积应该小于外框的80%
                            inner_contour = child_contour

                    # 添加边框对（外框，内框）
                    border_pairs.append({
                        'outer_contour': approx,
                        'inner_contour': inner_contour,
                        'area': area,
                        'bbox': (x, y, w, h)
                    })

        return border_pairs
    
    def detect_borders(self, frame, border_pairs):
        """处理检测到的黑色边框"""
        # 直接返回已经检测到的边框对
        return border_pairs
    
    def draw_borders(self, frame, border_pairs):
        """用绿色线条绘制黑色边框的内外框"""
        result_frame = frame.copy()

        for border in border_pairs:
            try:
                # 绘制外边框（黑色边框的外轮廓）
                cv2.drawContours(result_frame,
                               [border['outer_contour']],
                               -1,
                               self.settings.BORDER_COLOR_OUTER,
                               self.settings.BORDER_THICKNESS)

                # 绘制内边框（如果存在）
                if border['inner_contour'] is not None:
                    cv2.drawContours(result_frame,
                                   [border['inner_contour']],
                                   -1,
                                   self.settings.BORDER_COLOR_INNER,
                                   self.settings.BORDER_THICKNESS)

                # 添加标注信息
                x, y, w, h = border['bbox']
                cv2.putText(result_frame,
                          f"A4 Border",
                          (x, y-10),
                          cv2.FONT_HERSHEY_SIMPLEX,
                          0.6,
                          self.settings.BORDER_COLOR_OUTER,
                          2)

            except Exception as e:
                print(f"边框绘制失败: {e}")
                continue

        return result_frame
    
    def process_frame(self, frame):
        """处理单帧图像的主方法 - 专门检测黑色边框"""
        if not self.settings.DETECTION_ENABLED:
            return frame

        try:
            # 1. 检测黑色区域
            black_mask = self.detect_black_regions(frame)
            if black_mask is None:
                return frame

            # 2. 检测黑色区域的轮廓
            contours, hierarchy = self.detect_black_contours(black_mask)

            # 3. 查找黑色边框的内外框
            border_pairs = self.find_border_frames(contours, hierarchy)

            # 4. 处理边框信息
            border_info = self.detect_borders(frame, border_pairs)

            # 5. 绘制绿色边框线条
            result_frame = self.draw_borders(frame, border_info)

            # 缓存结果用于下一帧
            self.last_contours = contours

            return result_frame

        except Exception as e:
            print(f"帧处理失败: {e}")
            return frame
