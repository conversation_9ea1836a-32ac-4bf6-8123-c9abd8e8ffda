# -*- coding: utf-8 -*-
"""图像处理核心类 - 封装所有计算机视觉算法"""

import cv2
import numpy as np
import sys
import os

# 添加config目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.settings import CameraSettings

class ImageProcessor:
    """图像处理核心类 - 实现A4纸边框检测和绘制"""

    def __init__(self):
        """初始化图像处理器"""
        self.settings = CameraSettings
        self.last_contours = []  # 缓存上一帧的轮廓
        self.pixel_to_mm_ratio = None  # 像素到毫米的转换比例
        self.a4_center = None  # A4纸中心点
        self.mouse_click_point = None  # 鼠标点击点
        self.current_frame = None  # 当前帧，用于鼠标回调

    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.mouse_click_point = (x, y)
            print(f"鼠标点击位置: ({x}, {y})")

            # 如果有A4纸中心点，计算距离
            if self.a4_center and self.pixel_to_mm_ratio:
                distance_info = self.calculate_distance_between_points(
                    self.a4_center, self.mouse_click_point, self.pixel_to_mm_ratio)
                if distance_info:
                    print(f"距离A4纸中心: {distance_info['mm_distance']:.1f}mm")
    
    def detect_black_regions(self, frame):
        """检测黑色区域 - 专门用于识别黑色边框"""
        try:
            # 转换为HSV色彩空间，更适合颜色检测
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # 定义黑色范围 (HSV)
            # 黑色在HSV中：H可以是任意值，S较低，V很低
            lower_black = np.array([0, 0, 0])      # 下限
            upper_black = np.array([180, 255, 50]) # 上限(V=50检测深色)

            # 创建黑色掩码
            black_mask = cv2.inRange(hsv, lower_black, upper_black)

            # 形态学操作去除噪点
            kernel = np.ones((3, 3), np.uint8)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel)
            black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_OPEN, kernel)

            return black_mask

        except Exception as e:
            print(f"黑色区域检测失败: {e}")
            return None
    
    def detect_black_contours(self, black_mask):
        """检测黑色区域的轮廓"""
        try:
            # 查找轮廓 - 使用RETR_TREE获取层次结构
            contours, hierarchy = cv2.findContours(black_mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            return contours, hierarchy

        except Exception as e:
            print(f"黑色轮廓检测失败: {e}")
            return [], None
    
    def find_border_frames(self, contours, hierarchy):
        """查找黑色边框的内外框"""
        border_pairs = []

        if hierarchy is None:
            return border_pairs

        # 遍历轮廓，寻找符合条件的黑色边框
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            # 面积筛选 - 黑色边框应该有一定大小
            if area < self.settings.MIN_CONTOUR_AREA or area > self.settings.MAX_CONTOUR_AREA:
                continue

            # 轮廓近似为矩形
            epsilon = self.settings.CONTOUR_APPROX_EPSILON * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 检查是否为矩形（4个顶点）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = max(w, h) / min(w, h)

                # 检查宽高比是否符合A4纸
                if abs(aspect_ratio - self.settings.A4_ASPECT_RATIO) <= self.settings.ASPECT_RATIO_TOLERANCE:
                    # 查找内部轮廓（内边框）
                    inner_contour = None

                    # 检查是否有子轮廓（内边框）
                    child_idx = hierarchy[0][i][2]  # 第一个子轮廓
                    if child_idx != -1:
                        child_contour = contours[child_idx]
                        child_area = cv2.contourArea(child_contour)

                        # 内边框应该比外边框小
                        if child_area < area * 0.8:  # 内框面积应该小于外框的80%
                            inner_contour = child_contour

                    # 计算A4纸中心点
                    center_x = x + w // 2
                    center_y = y + h // 2

                    # 添加边框对（外框，内框，中心点）
                    border_pairs.append({
                        'outer_contour': approx,
                        'inner_contour': inner_contour,
                        'area': area,
                        'bbox': (x, y, w, h),
                        'center': (center_x, center_y)
                    })

        return border_pairs
    
    def detect_borders(self, frame, border_pairs):
        """处理检测到的黑色边框"""
        # 直接返回已经检测到的边框对
        return border_pairs

    def calculate_pixel_to_mm_ratio(self, border_pairs):
        """计算像素到毫米的转换比例"""
        if not border_pairs:
            return None

        # 使用第一个检测到的A4纸作为参考
        reference_paper = border_pairs[0]
        x, y, w, h = reference_paper['bbox']

        # 确定A4纸的方向（横向或纵向）
        if w > h:  # 横向放置
            pixel_width = w
            pixel_height = h
            real_width_mm = self.settings.A4_HEIGHT_MM  # 297mm
            real_height_mm = self.settings.A4_WIDTH_MM  # 210mm
        else:  # 纵向放置
            pixel_width = w
            pixel_height = h
            real_width_mm = self.settings.A4_WIDTH_MM   # 210mm
            real_height_mm = self.settings.A4_HEIGHT_MM # 297mm

        # 计算像素到毫米的比例（取宽度和高度的平均值）
        ratio_w = real_width_mm / pixel_width
        ratio_h = real_height_mm / pixel_height
        ratio = (ratio_w + ratio_h) / 2

        return ratio

    def calculate_distance_between_points(self, point1, point2, pixel_to_mm_ratio):
        """计算两点之间的距离"""
        if pixel_to_mm_ratio is None:
            return None

        # 计算像素距离
        dx = point2[0] - point1[0]
        dy = point2[1] - point1[1]
        pixel_distance = np.sqrt(dx*dx + dy*dy)

        # 转换为毫米
        mm_distance = pixel_distance * pixel_to_mm_ratio

        return {
            'pixel_distance': pixel_distance,
            'mm_distance': mm_distance,
            'dx_mm': dx * pixel_to_mm_ratio,
            'dy_mm': dy * pixel_to_mm_ratio
        }
    
    def draw_borders(self, frame, border_pairs):
        """用绿色线条绘制黑色边框的内外框，并显示距离测量（单张A4纸版本）"""
        result_frame = frame.copy()
        self.current_frame = result_frame  # 保存当前帧

        # 计算像素到毫米的转换比例
        pixel_to_mm_ratio = self.calculate_pixel_to_mm_ratio(border_pairs)
        self.pixel_to_mm_ratio = pixel_to_mm_ratio  # 保存比例供外部使用

        # 只处理第一张A4纸（因为只会有一张）
        if border_pairs:
            border = border_pairs[0]  # 只取第一张A4纸
            try:
                # 绘制外边框（黑色边框的外轮廓）
                cv2.drawContours(result_frame,
                               [border['outer_contour']],
                               -1,
                               self.settings.BORDER_COLOR_OUTER,
                               self.settings.BORDER_THICKNESS)

                # 绘制内边框（如果存在）
                if border['inner_contour'] is not None:
                    cv2.drawContours(result_frame,
                                   [border['inner_contour']],
                                   -1,
                                   self.settings.BORDER_COLOR_INNER,
                                   self.settings.BORDER_THICKNESS)

                # 绘制A4纸中心点（红色圆点）
                center_x, center_y = border['center']
                cv2.circle(result_frame,
                         (center_x, center_y),
                         self.settings.CENTER_POINT_RADIUS,
                         self.settings.CENTER_POINT_COLOR,
                         self.settings.CENTER_POINT_THICKNESS)

                # 收集中心点用于距离计算
                centers.append((center_x, center_y))

                # 添加标注信息
                x, y, w, h = border['bbox']
                cv2.putText(result_frame,
                          f"A4 Paper #{len(centers)}",
                          (x, y-10),
                          cv2.FONT_HERSHEY_SIMPLEX,
                          0.6,
                          self.settings.BORDER_COLOR_OUTER,
                          2)

                # 添加中心点坐标信息
                cv2.putText(result_frame,
                          f"({center_x}, {center_y})",
                          (center_x + 15, center_y - 15),
                          cv2.FONT_HERSHEY_SIMPLEX,
                          0.5,
                          self.settings.CENTER_POINT_COLOR,
                          1)

            except Exception as e:
                print(f"边框绘制失败: {e}")
                continue

        # 绘制距离测量线条和信息
        if self.settings.DISTANCE_MEASUREMENT_ENABLED and len(centers) >= 2 and pixel_to_mm_ratio:
            self.draw_distance_measurements(result_frame, centers, pixel_to_mm_ratio)

        # 显示比例信息
        if pixel_to_mm_ratio:
            cv2.putText(result_frame,
                      f"Scale: 1px = {pixel_to_mm_ratio:.3f}mm",
                      (10, 30),
                      cv2.FONT_HERSHEY_SIMPLEX,
                      0.7,
                      (255, 255, 255),
                      2)

        return result_frame

    def draw_distance_measurements(self, frame, centers, pixel_to_mm_ratio):
        """绘制距离测量线条和信息"""
        # 绘制所有中心点之间的距离
        for i in range(len(centers)):
            for j in range(i + 1, len(centers)):
                point1 = centers[i]
                point2 = centers[j]

                # 计算距离
                distance_info = self.calculate_distance_between_points(point1, point2, pixel_to_mm_ratio)

                if distance_info:
                    # 绘制连接线
                    cv2.line(frame, point1, point2, (255, 255, 0), 2)  # 黄色连接线

                    # 计算中点位置显示距离
                    mid_x = (point1[0] + point2[0]) // 2
                    mid_y = (point1[1] + point2[1]) // 2

                    # 显示距离信息
                    distance_mm = distance_info['mm_distance']
                    distance_text = f"{distance_mm:.{self.settings.DISTANCE_PRECISION}f}{self.settings.DISTANCE_UNIT}"

                    # 绘制距离文本背景
                    text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(frame,
                                (mid_x - text_size[0]//2 - 5, mid_y - text_size[1] - 5),
                                (mid_x + text_size[0]//2 + 5, mid_y + 5),
                                (0, 0, 0),
                                -1)

                    # 绘制距离文本
                    cv2.putText(frame,
                              distance_text,
                              (mid_x - text_size[0]//2, mid_y),
                              cv2.FONT_HERSHEY_SIMPLEX,
                              0.6,
                              (255, 255, 0),
                              2)
    
    def process_frame(self, frame):
        """处理单帧图像的主方法 - 专门检测黑色边框"""
        if not self.settings.DETECTION_ENABLED:
            return frame

        try:
            # 1. 检测黑色区域
            black_mask = self.detect_black_regions(frame)
            if black_mask is None:
                return frame

            # 2. 检测黑色区域的轮廓
            contours, hierarchy = self.detect_black_contours(black_mask)

            # 3. 查找黑色边框的内外框
            border_pairs = self.find_border_frames(contours, hierarchy)

            # 4. 处理边框信息
            border_info = self.detect_borders(frame, border_pairs)

            # 5. 绘制绿色边框线条
            result_frame = self.draw_borders(frame, border_info)

            # 缓存结果用于下一帧
            self.last_contours = contours

            return result_frame

        except Exception as e:
            print(f"帧处理失败: {e}")
            return frame
