# -*- coding: utf-8 -*-
"""图像处理核心类 - 封装所有计算机视觉算法"""

import cv2
import numpy as np
import sys
import os

# 添加config目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.settings import CameraSettings

class ImageProcessor:
    """图像处理核心类 - 实现A4纸边框检测和绘制"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.settings = CameraSettings
        self.last_contours = []  # 缓存上一帧的轮廓
    
    def preprocess_image(self, frame):
        """图像预处理：灰度化、高斯模糊、边缘检测(性能优化版)"""
        try:
            # 性能优化：缩小图像尺寸进行处理
            height, width = frame.shape[:2]
            scale_factor = 0.5  # 缩放因子
            small_frame = cv2.resize(frame, (int(width * scale_factor), int(height * scale_factor)))

            # 灰度化
            gray = cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY)

            # 高斯模糊降噪
            blurred = cv2.GaussianBlur(gray,
                                     (self.settings.GAUSSIAN_BLUR_SIZE, self.settings.GAUSSIAN_BLUR_SIZE),
                                     0)

            # Canny边缘检测
            edges = cv2.Canny(blurred,
                            self.settings.CANNY_THRESHOLD_LOW,
                            self.settings.CANNY_THRESHOLD_HIGH)

            # 恢复原始尺寸
            edges = cv2.resize(edges, (width, height))

            return edges

        except Exception as e:
            print(f"图像预处理失败: {e}")
            return None
    
    def detect_contours(self, edges):
        """轮廓检测"""
        try:
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            return contours
            
        except Exception as e:
            print(f"轮廓检测失败: {e}")
            return []
    
    def filter_paper_contours(self, contours):
        """筛选A4纸轮廓"""
        paper_contours = []
        
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            
            # 面积筛选
            if area < self.settings.MIN_CONTOUR_AREA or area > self.settings.MAX_CONTOUR_AREA:
                continue
            
            # 轮廓近似
            epsilon = self.settings.CONTOUR_APPROX_EPSILON * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 四边形筛选（A4纸应该是四边形）
            if len(approx) >= 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = max(w, h) / min(w, h)
                
                # 宽高比筛选（A4纸宽高比约为√2）
                if abs(aspect_ratio - self.settings.A4_ASPECT_RATIO) <= self.settings.ASPECT_RATIO_TOLERANCE:
                    paper_contours.append(contour)
        
        return paper_contours
    
    def detect_borders(self, frame, contours):
        """检测黑色边框的内外边界"""
        border_info = []
        
        for contour in contours:
            try:
                # 获取轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 提取ROI区域
                roi = frame[y:y+h, x:x+w]
                
                # 转换为灰度图
                gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                
                # 检测黑色边框（假设边框在轮廓边缘）
                # 这里简化处理，直接使用轮廓作为边框
                border_info.append({
                    'outer_contour': contour,
                    'inner_contour': contour,  # 简化处理，实际应该检测内边框
                    'roi_rect': (x, y, w, h)
                })
                
            except Exception as e:
                print(f"边框检测失败: {e}")
                continue
        
        return border_info
    
    def draw_borders(self, frame, border_info):
        """用绿色线条绘制边框"""
        result_frame = frame.copy()
        
        for border in border_info:
            try:
                # 绘制外边框
                cv2.drawContours(result_frame, 
                               [border['outer_contour']], 
                               -1, 
                               self.settings.BORDER_COLOR_OUTER, 
                               self.settings.BORDER_THICKNESS)
                
                # 绘制内边框（这里简化为同一轮廓）
                cv2.drawContours(result_frame, 
                               [border['inner_contour']], 
                               -1, 
                               self.settings.BORDER_COLOR_INNER, 
                               self.settings.BORDER_THICKNESS)
                
            except Exception as e:
                print(f"边框绘制失败: {e}")
                continue
        
        return result_frame
    
    def process_frame(self, frame):
        """处理单帧图像的主方法"""
        if not self.settings.DETECTION_ENABLED:
            return frame
        
        try:
            # 1. 图像预处理
            edges = self.preprocess_image(frame)
            if edges is None:
                return frame
            
            # 2. 轮廓检测
            contours = self.detect_contours(edges)
            
            # 3. 筛选A4纸轮廓
            paper_contours = self.filter_paper_contours(contours)
            
            # 4. 检测边框
            border_info = self.detect_borders(frame, paper_contours)
            
            # 5. 绘制边框
            result_frame = self.draw_borders(frame, border_info)
            
            # 缓存轮廓用于下一帧
            self.last_contours = paper_contours
            
            return result_frame
            
        except Exception as e:
            print(f"帧处理失败: {e}")
            return frame
