# -*- coding: utf-8 -*-
"""摄像头幕布显示主程序"""

import cv2
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from camera_capture import CameraCapture
from config.settings import CameraSettings
from image_processor import ImageProcessor

def main():
    """主程序 - 实现摄像头幕布实时显示和边框检测"""
    print("启动摄像头幕布显示系统...")

    # 初始化摄像头
    camera = CameraCapture()

    if not camera.is_opened():
        print("摄像头初始化失败，程序退出")
        return

    # 初始化图像处理器
    processor = ImageProcessor()
    print(f"边框检测功能: {'启用' if CameraSettings.DETECTION_ENABLED else '禁用'}")

    # 显示摄像头信息
    width, height = camera.get_resolution()
    fps = camera.get_fps()
    print(f"摄像头分辨率: {width}x{height}")
    print(f"摄像头帧率: {fps:.1f} FPS")
    print("按 'q' 键或 'ESC' 键退出程序")

    frame_count = 0  # 帧计数器
    start_time = time.time()  # 开始时间

    try:
        # 创建显示窗口
        cv2.namedWindow(CameraSettings.WINDOW_NAME, cv2.WINDOW_AUTOSIZE)

        # 设置鼠标回调函数（如果支持GUI）
        try:
            cv2.setMouseCallback(CameraSettings.WINDOW_NAME, processor.mouse_callback)
            print("鼠标交互已启用 - 点击画面测量距离")
        except Exception as e:
            print(f"鼠标回调设置失败: {e}")
            print("将使用非交互模式")

        # 主显示循环
        while True:
            # 读取摄像头帧
            ret, frame = camera.read_frame()

            if not ret:
                print("无法读取摄像头数据，程序退出")
                break

            # 图像处理：边框检测和绘制
            processed_frame = processor.process_frame(frame)

            # 在幕布上实时显示处理后的摄像头内容
            cv2.imshow(CameraSettings.WINDOW_NAME, processed_frame)

            # 检测按键
            key = cv2.waitKey(CameraSettings.get_wait_key_delay()) & 0xFF

            # 退出条件
            if key == CameraSettings.EXIT_KEY or key == CameraSettings.ESC_KEY:
                print(CameraSettings.ERROR_MESSAGES['exit_normal'])
                break

            frame_count += 1  # 增加帧计数

    except KeyboardInterrupt:
        print("\n用户中断程序")

    except Exception as e:
        print(f"程序运行时发生错误: {e}")

    finally:
        # 清理资源
        camera.release()

        # 性能统计
        end_time = time.time()
        total_time = end_time - start_time
        avg_fps = frame_count / total_time if total_time > 0 else 0
        print(f"程序已退出，共处理 {frame_count} 帧")
        print(f"运行时间: {total_time:.2f}秒，平均帧率: {avg_fps:.2f} FPS")

if __name__ == "__main__":
    main()
