# -*- coding: utf-8 -*-
"""摄像头幕布显示主程序"""

import cv2
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from camera_capture import CameraCapture
from config.settings import CameraSettings

def main():
    """主程序 - 实现摄像头幕布实时显示"""
    print("启动摄像头幕布显示系统...")

    # 初始化摄像头
    camera = CameraCapture()

    if not camera.is_opened():
        print("摄像头初始化失败，程序退出")
        return

    # 显示摄像头信息
    width, height = camera.get_resolution()
    fps = camera.get_fps()
    print(f"摄像头分辨率: {width}x{height}")
    print(f"摄像头帧率: {fps:.1f} FPS")
    print("按 'q' 键或 'ESC' 键退出程序")

    frame_count = 0  # 帧计数器

    try:
        # 创建显示窗口
        cv2.namedWindow(CameraSettings.WINDOW_NAME, cv2.WINDOW_AUTOSIZE)

        # 主显示循环
        while True:
            # 读取摄像头帧
            ret, frame = camera.read_frame()

            if not ret:
                print("无法读取摄像头数据，程序退出")
                break

            # 在幕布上实时显示摄像头内容
            cv2.imshow(CameraSettings.WINDOW_NAME, frame)

            # 检测按键
            key = cv2.waitKey(CameraSettings.get_wait_key_delay()) & 0xFF

            # 退出条件
            if key == CameraSettings.EXIT_KEY or key == CameraSettings.ESC_KEY:
                print(CameraSettings.ERROR_MESSAGES['exit_normal'])
                break

            frame_count += 1  # 增加帧计数

    except KeyboardInterrupt:
        print("\n用户中断程序")

    except Exception as e:
        print(f"程序运行时发生错误: {e}")

    finally:
        # 清理资源
        camera.release()
        print(f"程序已退出，共处理 {frame_count} 帧")

if __name__ == "__main__":
    main()
