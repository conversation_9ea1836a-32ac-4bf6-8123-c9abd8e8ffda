# -*- coding: utf-8 -*-
"""摄像头幕布显示主程序"""

import cv2
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from camera_capture import CameraCapture
from config.settings import CameraSettings

def main():
    """主程序 - 实现摄像头幕布实时显示"""
    print("启动摄像头幕布显示系统...")

    # 初始化摄像头
    camera = CameraCapture()

    if not camera.is_opened():
        print("摄像头初始化失败，程序退出")
        return

    # 显示摄像头信息
    width, height = camera.get_resolution()
    fps = camera.get_fps()
    print(f"摄像头分辨率: {width}x{height}")
    print(f"摄像头帧率: {fps:.1f} FPS")
    print("由于GUI支持问题，将保存摄像头图像到文件")
    print("按 Ctrl+C 退出程序")

    # 创建输出目录
    output_dir = "camera_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    frame_count = 0

    try:
        # 主显示循环
        while True:
            # 读取摄像头帧
            ret, frame = camera.read_frame()

            if not ret:
                print("无法读取摄像头数据，程序退出")
                break

            # 保存当前帧到文件（每秒保存一帧）
            if frame_count % 30 == 0:  # 每30帧保存一次
                filename = f"{output_dir}/frame_{frame_count:06d}.jpg"
                cv2.imwrite(filename, frame)
                print(f"保存帧: {filename}")

            frame_count += 1

            # 控制帧率
            time.sleep(1.0 / CameraSettings.FPS_LIMIT)

    except KeyboardInterrupt:
        print("\n用户中断程序")

    except Exception as e:
        print(f"程序运行时发生错误: {e}")

    finally:
        # 清理资源
        camera.release()
        print(f"程序已退出，共处理 {frame_count} 帧")

if __name__ == "__main__":
    main()
