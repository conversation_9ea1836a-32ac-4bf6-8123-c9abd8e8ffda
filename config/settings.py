# -*- coding: utf-8 -*-
"""摄像头配置管理模块 - 统一管理所有可配置参数"""

class CameraSettings:
    """摄像头系统配置类"""
    
    # 摄像头相关配置
    CAMERA_ID = 0  # 摄像头设备ID，0为默认摄像头
    WINDOW_NAME = "摄像头实时显示"  # 显示窗口标题
    WINDOW_WIDTH = 1920  # 窗口宽度
    WINDOW_HEIGHT = 1080  # 窗口高度
    
    # 控制相关配置
    EXIT_KEY = ord('q')  # 退出键，按q键退出
    ESC_KEY = 27  # ESC键退出
    FPS_LIMIT = 30  # 帧率限制
    
    # 显示相关配置
    WINDOW_FLAGS = 0  # 窗口标志，0为默认
    AUTO_SIZE = True  # 自动调整窗口大小
    
    # 错误提示信息
    ERROR_MESSAGES = {
        'camera_not_found': '错误：无法找到摄像头设备',
        'camera_init_failed': '错误：摄像头初始化失败',
        'frame_read_failed': '警告：读取视频帧失败',
        'exit_normal': '程序正常退出'
    }
    
    @classmethod
    def validate_config(cls):
        """验证配置参数的有效性"""
        errors = []
        
        if cls.CAMERA_ID < 0:
            errors.append("摄像头ID不能为负数")
        
        if cls.WINDOW_WIDTH <= 0 or cls.WINDOW_HEIGHT <= 0:
            errors.append("窗口尺寸必须大于0")
        
        if cls.FPS_LIMIT <= 0:
            errors.append("帧率限制必须大于0")
        
        return errors
    
    @classmethod
    def get_wait_key_delay(cls):
        """计算waitKey延迟时间"""
        return max(1, int(1000 / cls.FPS_LIMIT))  # 毫秒
