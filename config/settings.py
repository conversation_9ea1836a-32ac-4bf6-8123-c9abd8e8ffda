# -*- coding: utf-8 -*-
"""摄像头配置管理模块 - 统一管理所有可配置参数"""

class CameraSettings:
    """摄像头系统配置类"""
    
    # 摄像头相关配置
    CAMERA_ID = 0  # 摄像头设备ID，0为默认摄像头
    WINDOW_NAME = "摄像头实时显示"  # 显示窗口标题
    WINDOW_WIDTH = 1920  # 窗口宽度
    WINDOW_HEIGHT = 1080  # 窗口高度
    
    # 控制相关配置
    EXIT_KEY = ord('q')  # 退出键，按q键退出
    ESC_KEY = 27  # ESC键退出
    FPS_LIMIT = 30  # 帧率限制
    
    # 显示相关配置
    WINDOW_FLAGS = 0  # 窗口标志，0为默认
    AUTO_SIZE = True  # 自动调整窗口大小

    # 图像处理配置
    CANNY_THRESHOLD_LOW = 100  # Canny边缘检测低阈值(优化性能)
    CANNY_THRESHOLD_HIGH = 200  # Canny边缘检测高阈值(优化性能)
    GAUSSIAN_BLUR_SIZE = 3  # 高斯模糊核大小(减小提升性能)

    # 轮廓检测配置
    MIN_CONTOUR_AREA = 10000  # 最小轮廓面积(增大减少计算)
    MAX_CONTOUR_AREA = 300000  # 最大轮廓面积(减小提升性能)
    CONTOUR_APPROX_EPSILON = 0.05  # 轮廓近似精度(增大提升性能)

    # 边框绘制配置
    BORDER_COLOR_INNER = (0, 255, 0)  # 内边框颜色(绿色BGR)
    BORDER_COLOR_OUTER = (0, 255, 0)  # 外边框颜色(绿色BGR)
    BORDER_THICKNESS = 2  # 边框线条粗细

    # A4纸检测配置
    A4_ASPECT_RATIO = 1.414  # A4纸宽高比(√2)
    ASPECT_RATIO_TOLERANCE = 0.3  # 宽高比容差
    DETECTION_ENABLED = True  # 是否启用边框检测
    
    # 错误提示信息
    ERROR_MESSAGES = {
        'camera_not_found': '错误：无法找到摄像头设备',
        'camera_init_failed': '错误：摄像头初始化失败',
        'frame_read_failed': '警告：读取视频帧失败',
        'exit_normal': '程序正常退出'
    }
    
    @classmethod
    def validate_config(cls):
        """验证配置参数的有效性"""
        errors = []

        # 基础配置验证
        if cls.CAMERA_ID < 0:
            errors.append("摄像头ID不能为负数")

        if cls.WINDOW_WIDTH <= 0 or cls.WINDOW_HEIGHT <= 0:
            errors.append("窗口尺寸必须大于0")

        if cls.FPS_LIMIT <= 0:
            errors.append("帧率限制必须大于0")

        # 图像处理配置验证
        if cls.CANNY_THRESHOLD_LOW <= 0 or cls.CANNY_THRESHOLD_HIGH <= 0:
            errors.append("Canny阈值必须大于0")

        if cls.CANNY_THRESHOLD_LOW >= cls.CANNY_THRESHOLD_HIGH:
            errors.append("Canny低阈值必须小于高阈值")

        if cls.GAUSSIAN_BLUR_SIZE <= 0 or cls.GAUSSIAN_BLUR_SIZE % 2 == 0:
            errors.append("高斯模糊核大小必须为正奇数")

        if cls.MIN_CONTOUR_AREA <= 0 or cls.MAX_CONTOUR_AREA <= 0:
            errors.append("轮廓面积范围必须大于0")

        if cls.MIN_CONTOUR_AREA >= cls.MAX_CONTOUR_AREA:
            errors.append("最小轮廓面积必须小于最大轮廓面积")

        if cls.BORDER_THICKNESS <= 0:
            errors.append("边框线条粗细必须大于0")

        return errors
    
    @classmethod
    def get_wait_key_delay(cls):
        """计算waitKey延迟时间"""
        return max(1, int(1000 / cls.FPS_LIMIT))  # 毫秒
