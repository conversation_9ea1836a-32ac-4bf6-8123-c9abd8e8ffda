# -*- coding: utf-8 -*-
"""测试摄像头到A4纸中心的深度距离测量功能"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.image_processor import ImageProcessor
from config.settings import CameraSettings

def create_test_images_different_sizes():
    """创建不同尺寸的A4纸测试图像，模拟不同距离"""
    test_cases = [
        {"name": "近距离", "a4_size": (400, 300), "position": (200, 150)},
        {"name": "中距离", "a4_size": (300, 225), "position": (250, 187)},
        {"name": "远距离", "a4_size": (200, 150), "position": (300, 225)},
    ]
    
    images = []
    
    for case in test_cases:
        # 创建白色背景
        img = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # 绘制A4纸
        w, h = case["a4_size"]
        x, y = case["position"]
        
        # 外框
        cv2.rectangle(img, (x, y), (x + w, y + h), (0, 0, 0), 15)
        
        # 内框
        inner_margin = 10
        cv2.rectangle(img, 
                     (x + inner_margin, y + inner_margin), 
                     (x + w - inner_margin, y + h - inner_margin), 
                     (0, 0, 0), 10)
        
        images.append({
            "image": img,
            "name": case["name"],
            "expected_size": case["a4_size"]
        })
    
    return images

def test_depth_calculation():
    """测试深度计算功能"""
    print("=== 摄像头深度距离测量测试 ===")
    
    # 创建不同尺寸的测试图像
    test_images = create_test_images_different_sizes()
    
    # 初始化图像处理器
    processor = ImageProcessor()
    
    print(f"📷 摄像头参数:")
    print(f"   焦距: {CameraSettings.CAMERA_FOCAL_LENGTH_MM}mm")
    print(f"   传感器宽度: {CameraSettings.SENSOR_WIDTH_MM}mm")
    print(f"   A4纸尺寸: {CameraSettings.A4_WIDTH_MM}mm × {CameraSettings.A4_HEIGHT_MM}mm")
    print()
    
    results = []
    
    for i, test_case in enumerate(test_images):
        print(f"测试 {i+1}: {test_case['name']}")
        
        # 处理图像
        result_img = processor.process_frame(test_case["image"])
        
        # 获取深度信息
        if processor.depth_distance:
            depth_info = processor.depth_distance
            print(f"   检测到的A4纸像素尺寸: {test_case['expected_size']}")
            print(f"   计算的距离: {depth_info['depth_cm']:.1f}cm ({depth_info['depth_m']:.2f}m)")
            print(f"   A4纸方向: {depth_info['a4_orientation']}")
            print(f"   使用的参考尺寸: {depth_info['real_width_used']}mm")
            
            results.append({
                "name": test_case["name"],
                "distance_cm": depth_info['depth_cm'],
                "pixel_size": test_case['expected_size']
            })
        else:
            print("   ❌ 未检测到A4纸或深度计算失败")
        
        # 保存结果图像
        filename = f"test_depth_{test_case['name']}.jpg"
        cv2.imwrite(filename, result_img)
        print(f"   结果保存为: {filename}")
        print()
    
    return results

def analyze_depth_accuracy():
    """分析深度测量的准确性"""
    print("=== 深度测量准确性分析 ===")
    
    # 理论计算：如果A4纸在不同距离下的像素尺寸
    theoretical_distances = [30, 50, 100, 150, 200]  # 厘米
    
    print("理论距离 vs 像素尺寸关系:")
    print("距离(cm) | A4宽度像素 | A4高度像素")
    print("-" * 40)
    
    for distance_cm in theoretical_distances:
        # 使用深度公式反推像素尺寸
        # 像素尺寸 = (实际尺寸 × 焦距 × 图像宽度) / (距离 × 传感器宽度)
        frame_width = 800  # 测试图像宽度
        distance_mm = distance_cm * 10
        
        pixel_width = (CameraSettings.A4_WIDTH_MM * CameraSettings.CAMERA_FOCAL_LENGTH_MM * frame_width) / \
                     (distance_mm * CameraSettings.SENSOR_WIDTH_MM)
        
        pixel_height = (CameraSettings.A4_HEIGHT_MM * CameraSettings.CAMERA_FOCAL_LENGTH_MM * frame_width) / \
                      (distance_mm * CameraSettings.SENSOR_WIDTH_MM)
        
        print(f"{distance_cm:6d}   | {pixel_width:10.1f} | {pixel_height:11.1f}")
    
    print("\n💡 使用提示:")
    print("1. 确保A4纸完全平行于摄像头")
    print("2. A4纸应该完全在画面中")
    print("3. 光照条件要良好，边框清晰")
    print("4. 可以通过调整摄像头参数提高精度")

def main():
    """主测试函数"""
    print("开始摄像头深度距离测量功能测试...\n")
    
    try:
        # 深度计算测试
        results = test_depth_calculation()
        
        # 准确性分析
        analyze_depth_accuracy()
        
        if results:
            print("\n🎉 深度测量功能测试成功！")
            print("\n📊 测试结果汇总:")
            for result in results:
                print(f"- {result['name']}: {result['distance_cm']:.1f}cm")
            
            print("\n🔍 新功能特点:")
            print("- 基于A4纸尺寸的深度估算")
            print("- 自动识别A4纸方向")
            print("- 实时显示摄像头距离")
            print("- 支持厘米和米单位显示")
            print("- 显示详细的测量信息")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
